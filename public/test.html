<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Indigo Insurance - License Verification</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background: #f8fafc; }
        h1 { color: #1e3a8a; text-align: center; margin-bottom: 30px; }
        .section { margin: 20px 0; padding: 30px; border: 1px solid #e2e8f0; border-radius: 8px; background: white; box-shadow: 0 1px 3px rgba(0,0,0,0.1); }
        input, textarea, button { margin: 8px 0; padding: 12px; width: 100%; box-sizing: border-box; border: 1px solid #d1d5db; border-radius: 4px; }
        button { background: #1e3a8a; color: white; border: none; cursor: pointer; font-weight: 600; }
        button:hover { background: #1e40af; }
        .result { margin-top: 15px; padding: 12px; border-radius: 4px; }
        .error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .success { background: #f0fdf4; color: #16a34a; border: 1px solid #bbf7d0; }
    </style>
</head>
<body>
    <h1>Indigo Insurance - Create License Verification Ticket</h1>

    <!-- Create Ticket Section -->
    <div class="section">
        <h2>Create License Verification Ticket</h2>
        <input type="email" id="email" placeholder="Customer email address" value="">
        <input type="text" id="reason" placeholder="Reason for license verification" value="">
        <input type="text" id="policy_id" placeholder="Policy ID" value="">
        <button onclick="createTicket()">Create Ticket</button>
        <div id="createResult" class="result" style="display:none;"></div>
    </div>

    <script>
        async function createTicket() {
            const email = document.getElementById('email').value;
            const reason = document.getElementById('reason').value;
            const policy_id = document.getElementById('policy_id').value;

            if (!email || !reason || !policy_id) {
                showResult('createResult', 'Please fill all fields', 'error');
                return;
            }

            try {
                showResult('createResult', 'Creating ticket...', 'success');

                const response = await fetch('/api/create-ticket', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        email: email,
                        reason: reason,
                        policy_id: policy_id
                    })
                });

                const result = await response.json();

                if (response.ok) {
                    showResult('createResult', `✅ Ticket created successfully! ID: ${result.ticket_id}`, 'success');
                } else {
                    showResult('createResult', `❌ Error: ${result.error}`, 'error');
                }
            } catch (error) {
                showResult('createResult', `❌ Network error: ${error.message}`, 'error');
            }
        }

        function showResult(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
            element.style.display = 'block';
        }
    </script>
</body>
</html>
