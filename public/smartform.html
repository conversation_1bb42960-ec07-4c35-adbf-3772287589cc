<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Indigo</title>
  </head>
  <body>
    <div id="smartFormContainer" style="width: 75% !important" v-else>
      <rozieai-smart-form
        id="ac-connex-sf"
        class="hydrated"
        app="rozi_qats_sm"
        language="en_us"
        init-skill="Connx_supportRequest"
        version="0.2"
      ></rozieai-smart-form>
    </div>

    <script
      src="https://rozie-shared-webchat-sandbox.rozie.ai/rozieai.esm.js"
      type="module"
    ></script>

    <script>
      function getUrlParam(param) {
        const urlParams = new URLSearchParams(window.location.search);
        return urlParams.get(param) || "Test";
      }

      const policyNo = getUrlParam("policyNo");
      const ticketId = getUrlParam("ticketId");

      // Set initial localStorage on load
      (function () {
        localStorage.removeItem("rozie-smart-form_msgs");
        localStorage.removeItem("rozie-smartform_key");

        setTimeout(() => {
          const localStorageData = localStorage.getItem("localStorage");
          const storageObject = localStorageData ? JSON.parse(localStorageData) : {};
          storageObject.user = {
            policyNo: policyNo,
            ticketId: ticketId
          };
          localStorage.setItem("localStorage", JSON.stringify(storageObject));
        }, 100);
      })();

      // On button click, reset and re-add the smart form with new localStorage
      const btn = document.getElementById("open-ticket-btn");

      btn.addEventListener("click", () => {
        const acConnexSF = document.getElementById("ac-connex-sf");

        if (acConnexSF != null) {
          localStorage.removeItem("rozie-smart-form_msgs");
          acConnexSF.parentNode.removeChild(acConnexSF);
        }

        const acConnexSFElement = document.createElement("rozieai-smart-form");
        acConnexSFElement.setAttribute("id", "ac-connex-sf");
        acConnexSFElement.setAttribute("class", "hydrated");
        acConnexSFElement.setAttribute("app", "rozi_qats_sm");
        acConnexSFElement.setAttribute("init-skill", "Connx_supportRequest");
        acConnexSFElement.setAttribute("version", "0.2");
        acConnexSFElement.setAttribute("language", "en_us");

        document.getElementById("smartFormContainer").appendChild(acConnexSFElement);

        setTimeout(() => {
          const localStorageData = localStorage.getItem("localStorage");
          const storageObject = localStorageData ? JSON.parse(localStorageData) : {};
          storageObject.user = {
            policyNo: policyNo,
            ticketId: ticketId
          };
          localStorage.setItem("localStorage", JSON.stringify(storageObject));
        }, 100);
      });
    </script>
  </body>
</html>
