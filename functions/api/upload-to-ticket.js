export async function onRequest(context) {
  // Handle CORS
  if (context.request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      }
    });
  }

  if (context.request.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }

  try {
    const data = await context.request.json();
    const concepts = data.concepts || {};
    const base64_obj = concepts.base64 || {};
    const base64_str = base64_obj.base64;
    const ticket_id = concepts.ticketId?.ticketId;
    const policy_id = concepts.policyNo?.policyNo;
    const license_expiry = concepts.licenseExpiry?.licenseExpiry;

    if (!base64_str || !ticket_id) {
      return new Response(JSON.stringify({
        error: "Missing base64 or ticket ID"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    }

    // Get environment variables
    const subdomain = context.env.ZENDESK_SUBDOMAIN;
    const zendesk_email = context.env.ZENDESK_EMAIL;
    const token = context.env.ZENDESK_TOKEN;

    if (!subdomain || !zendesk_email || !token) {
      return new Response(JSON.stringify({
        error: "Missing Zendesk configuration"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    }

    // Clean base64 string (remove data URL prefix if present)
    let cleanBase64 = base64_str;
    if (base64_str.includes(',')) {
      cleanBase64 = base64_str.split(',')[1];
    }

    // Determine file type from base64 header
    let mimeType = 'image/png';
    let fileName = 'uploaded_image.png';

    if (base64_str.includes('data:image/')) {
      const mimeMatch = base64_str.match(/data:image\/([^;]+)/);
      if (mimeMatch) {
        const imageType = mimeMatch[1];
        mimeType = `image/${imageType}`;
        fileName = `uploaded_image.${imageType}`;
      }
    }

    // Convert base64 to binary data manually
    const binaryString = atob(cleanBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }

    // Upload file to Zendesk using raw binary data
    const uploadResponse = await fetch(`https://${subdomain}.zendesk.com/api/v2/uploads.json?filename=${fileName}`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${btoa(`${zendesk_email}/token:${token}`)}`,
        'Content-Type': mimeType
      },
      body: bytes
    });

    if (!uploadResponse.ok) {
      const errorText = await uploadResponse.text();
      throw new Error(`Upload failed: ${uploadResponse.status} - ${errorText}`);
    }

    const uploadResult = await uploadResponse.json();
    const uploadToken = uploadResult.upload.token;

    // Get ticket details to find the requester
    const ticketResponse = await fetch(`https://${subdomain}.zendesk.com/api/v2/tickets/${ticket_id}.json`, {
      method: 'GET',
      headers: {
        'Authorization': `Basic ${btoa(`${zendesk_email}/token:${token}`)}`,
        'Content-Type': 'application/json'
      }
    });

    const ticketData = await ticketResponse.json();
    const requesterId = ticketData.ticket.requester_id;

    // Prepare comment body with license information
    let commentBody = "Document uploaded via smart form";
    if (license_expiry) {
      commentBody += `\n\nLicense Expiry Date: ${license_expiry}`;
    }
    if (policy_id) {
      commentBody += `\nPolicy ID: ${policy_id}`;
    }

    // Attach the file to the ticket as a private comment from the requester (no email notifications)
    const ticketUpdateData = {
      ticket: {
        comment: {
          body: commentBody,
          public: false,
          uploads: [uploadToken],
          author_id: requesterId
        }
      }
    };

    const updateResponse = await fetch(`https://${subdomain}.zendesk.com/api/v2/tickets/${ticket_id}.json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(`${zendesk_email}/token:${token}`)}`
      },
      body: JSON.stringify(ticketUpdateData)
    });

    if (!updateResponse.ok) {
      const errorText = await updateResponse.text();
      throw new Error(`Failed to update ticket: ${updateResponse.status} - ${errorText}`);
    }

    return new Response(JSON.stringify({
      status: "success",
      message: `Image uploaded to ticket ${ticket_id}${license_expiry ? ` with license expiry ${license_expiry}` : ''}`
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }
}
