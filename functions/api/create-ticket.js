export async function onRequest(context) {
  // Handle CORS
  if (context.request.method === "OPTIONS") {
    return new Response(null, {
      status: 200,
      headers: {
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Methods": "POST, OPTIONS",
        "Access-Control-Allow-Headers": "Content-Type",
      }
    });
  }

  if (context.request.method !== "POST") {
    return new Response(JSON.stringify({ error: "Method not allowed" }), {
      status: 405,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }

  try {
    const data = await context.request.json();
    const { email, reason, policy_id } = data;

    if (!email || !reason || !policy_id) {
      return new Response(JSON.stringify({
        error: "Missing required fields: email, reason, policy_id"
      }), {
        status: 400,
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    }

    // Get environment variables
    const subdomain = context.env.ZENDESK_SUBDOMAIN;
    const zendesk_email = context.env.ZENDESK_EMAIL;
    const token = context.env.ZENDESK_TOKEN;

    if (!subdomain || !zendesk_email || !token) {
      return new Response(JSON.stringify({
        error: "Missing Zendesk configuration"
      }), {
        status: 500,
        headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
      });
    }

    // Create Zendesk ticket
    const ticketData = {
      ticket: {
        subject: `License Verification Required - Policy ${policy_id}`,
        comment: {
          body: `Reason: ${reason}\nPolicy ID: ${policy_id}`
        },
        requester: {
          name: "Indigo Insurance",
          email: email
        },
        priority: "normal",
        type: "question",
        status: "pending",
        tags: ["license-verification", "pending-client-reply"]
      }
    };

    const zendeskResponse = await fetch(`https://${subdomain}.zendesk.com/api/v2/tickets.json`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(`${zendesk_email}/token:${token}`)}`
      },
      body: JSON.stringify(ticketData)
    });

    if (!zendeskResponse.ok) {
      const errorText = await zendeskResponse.text();
      throw new Error(`Zendesk API error: ${zendeskResponse.status} - ${errorText}`);
    }

    const ticket = await zendeskResponse.json();
    const ticketId = ticket.ticket.id;

    // Send follow-up email with smart form link
    const smartFormUrl = `https://indigo-automation-poc.pages.dev/smartform.html?ticketId=${ticketId}&policyNo=${policy_id}`;

    const emailMessage = `Dear Customer,

We need to verify your license documentation for Policy ${policy_id}.

Reason: ${reason}

Please upload your license documents using this secure form:
${smartFormUrl}

We'll review your submission and get back to you soon.

Best regards,
Indigo Insurance Team`;

    // Add email as comment to ticket
    const commentData = {
      ticket: {
        comment: {
          body: emailMessage,
          public: true
        }
      }
    };

    await fetch(`https://${subdomain}.zendesk.com/api/v2/tickets/${ticketId}.json`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${btoa(`${zendesk_email}/token:${token}`)}`
      },
      body: JSON.stringify(commentData)
    });

    return new Response(JSON.stringify({
      ticket_id: ticketId,
      status: "created"
    }), {
      status: 200,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });

  } catch (error) {
    return new Response(JSON.stringify({
      error: error.message
    }), {
      status: 500,
      headers: { "Content-Type": "application/json", "Access-Control-Allow-Origin": "*" }
    });
  }
}
