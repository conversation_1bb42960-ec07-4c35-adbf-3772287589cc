# 🎫 Indigo Automation POC - Zendesk Integration

A complete proof-of-concept for a 2-phase Zendesk workflow using Cloudflare Pages Functions (Python) - entirely on the free tier.

## 🎯 What This Does

**Phase 1: Ticket Creation**
- API endpoint accepts `email`, `reason`, `policy_id`
- Creates a Zendesk ticket
- Sends email to user with smart form link containing `ticketId` and `policyNo`

**Phase 2: Image Upload**
- API endpoint accepts base64 image data with ticket/policy info
- Uploads image as attachment to the existing Zendesk ticket

## 🏗️ Project Structure

```
indigo-automation-poc/
├── functions/
│   ├── create_ticket.py         # API: Create ticket + send email
│   └── upload_to_ticket.py      # API: Upload image to ticket
├── public/
│   ├── smartform.html           # Smart form for image upload
│   └── test.html               # API testing interface
├── wrangler.toml               # Cloudflare configuration
└── README.md                   # This file
```

## 🚀 Quick Start

### 1. Deploy to Cloudflare Pages

**Option A: GitHub Integration (Recommended)**
1. Push this code to a GitHub repository
2. Go to [Cloudflare Dashboard](https://dash.cloudflare.com) → Pages
3. Click "Create a project" → "Connect to Git"
4. Select your repository
5. Configure build settings:
   - **Build output directory**: `public`
   - **Root directory**: `/` (leave empty)
6. Click "Save and Deploy"

**Option B: Direct Upload**
1. Install Wrangler CLI: `npm install -g wrangler`
2. Login: `wrangler login`
3. Deploy: `wrangler pages deploy public --project-name indigo-automation-poc`

### 2. Configure Environment Variables

**IMPORTANT**: Set these in Cloudflare Dashboard before testing:
1. Go to Pages → indigo-automation-poc → Settings → Environment Variables
2. Add:
   - `ZENDESK_SUBDOMAIN`: Your Zendesk subdomain
   - `ZENDESK_EMAIL`: Your Zendesk agent email
   - `ZENDESK_TOKEN`: Your Zendesk API token

### 3. Test the APIs

Once deployed, your APIs will be available at:
- `https://indigo-automation-poc.pages.dev/api/create-ticket`
- `https://indigo-automation-poc.pages.dev/api/upload-to-ticket`

**Test Interface**: Visit `https://indigo-automation-poc.pages.dev/test.html`

## 📡 API Documentation

### Create Ticket API

**Endpoint**: `POST /api/create-ticket`

**Request Body**:
```json
{
  "email": "<EMAIL>",
  "reason": "Need help with policy claim",
  "policy_id": "POLICY123"
}
```

**Response**:
```json
{
  "ticket_id": "12345",
  "status": "created"
}
```

**What happens**:
1. Creates Zendesk ticket
2. Sends email to user with smart form link:
   `https://indigo-automation-poc.pages.dev/smartform.html?ticketId=12345&policyNo=POLICY123`

### Upload Image API

**Endpoint**: `POST /api/upload-to-ticket`

**Request Body** (Rozie format):
```json
{
  "rozie_user_id": "user123",
  "channel_user_id": "channel456",
  "skill_id": "skill_id_get_product",
  "skill_label": "skill_label_get_product",
  "concepts": {
    "ticketId": { "ticketId": "12345" },
    "policyNo": { "policyNo": "POLICY123" },
    "base64": { "base64": "iVBORw0KGgoAAAANSUhEUgAA..." },
    "licenseExpiry": { "licenseExpiry": "2024-12-31" }
  }
}
```

**Response**:
```json
{
  "status": "success",
  "message": "Image uploaded to ticket 12345 with license expiry 2024-12-31"
}
```

**What happens**:
1. Uploads image as attachment to existing Zendesk ticket
2. Adds private comment with the attachment and license information
3. If license expiry is provided, includes license expiry date in the comment body

## 🔧 Configuration

### Zendesk Credentials

**SECURE**: Credentials are now stored as environment variables. Set these in Cloudflare Dashboard:

1. Go to Pages → indigo-automation-poc → Settings → Environment Variables
2. Add:
   - `ZENDESK_SUBDOMAIN`: Your Zendesk subdomain (e.g., "your-company")
   - `ZENDESK_EMAIL`: Your Zendesk agent email
   - `ZENDESK_TOKEN`: Your Zendesk API token

### Smart Form Configuration

The smart form (`smartform.html`) automatically:
- Extracts `ticketId` and `policyNo` from URL parameters
- Stores them in localStorage for Rozie integration
- Loads the Rozie smart form component

## 🧪 Testing Workflow

1. **Test Ticket Creation**:
   ```bash
   curl -X POST https://indigo-automation-poc.pages.dev/api/create-ticket \
     -H "Content-Type: application/json" \
     -d '{"email":"<EMAIL>","reason":"Test issue","policy_id":"TEST123"}'
   ```

2. **Test Image Upload**:
   ```bash
   # Convert image to base64 first
   base64 -i image.png | tr -d '\n' > image.b64

   curl -X POST https://indigo-automation-poc.pages.dev/api/upload-to-ticket \
     -H "Content-Type: application/json" \
     -d '{
       "concepts": {
         "ticketId": {"ticketId": "TICKET_ID_FROM_STEP_1"},
         "policyNo": {"policyNo": "TEST123"},
         "base64": {"base64": "'$(cat image.b64)'"},
         "licenseExpiry": {"licenseExpiry": "2024-12-31"}
       }
     }'
   ```

3. **Test Smart Form**:
   Visit: `https://indigo-automation-poc.pages.dev/smartform.html?ticketId=123&policyNo=TEST123`

## 🔍 Troubleshooting

### Common Issues

1. **"Function not found" errors**:
   - Ensure functions are in `/functions/` directory
   - Check file names match the URL paths

2. **CORS errors**:
   - APIs include CORS headers for browser requests
   - Test with curl first to isolate issues

3. **Zendesk API errors**:
   - Verify credentials in wrangler.toml
   - Check Zendesk API token permissions

### Logs

View function logs in Cloudflare Dashboard:
Pages → Your Project → Functions → View logs

## 🎯 Next Steps

- [ ] Move credentials to environment variables
- [ ] Add input validation and error handling
- [ ] Implement webhook for real-time notifications
- [ ] Add authentication for API endpoints
- [ ] Set up monitoring and alerts

## 💡 Free Tier Limits

Cloudflare Pages Functions (free tier):
- ✅ 100,000 requests/day
- ✅ 10ms CPU time per request
- ✅ Unlimited bandwidth
- ✅ Custom domains

Perfect for POCs and small-scale production use!
