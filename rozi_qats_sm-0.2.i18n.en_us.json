{"API_ENDPOINTS": {"API_BASE": "https://rozie-shared-webchat-adpter-sandbox.rozie.ai/", "API_VERSION": "v2", "COMMUNICATION_METHOD": "sync"}, "APP_META_DATA": {"product-name": "concierge", "domain-id": "domain_64cf5c48-2ae5-11ea-978f-2e728ce63985", "domain-prefix": "rozi", "domain-name": "<PERSON><PERSON><PERSON>", "client-id": "client_r4f64bbe-3de5-22ea-978f-2e728ce90438", "client-prefix": "rozi", "client-name": "<PERSON><PERSON><PERSON>", "project-id": "project_f286397d-12c8-4028-aec1-3524c5e642e5", "project-prefix": "qats", "project-name": "QA", "application-id": "application_d150e3ca-fcc1-4231-9737-80e48a8e8016", "application-tag": "rozi_rozi_qats_sbx", "environment": "sandbox", "channel": "smart_form", "language": "en_us", "is-test-request": "false"}, "CONCEPT_MAPPING": [{"name": "policyNo", "path": "localStorage.user.policyNo", "initial_load": true, "source": "LocalStorage"}, {"name": "ticketId", "path": "localStorage.user.ticketId", "initial_load": true, "source": "LocalStorage"}], "STYLES": {"header": {"background": "#352296", "color": "#ffffff", "fontWeight": "600", "text": "Indigo Insurance", "boxShadow": "2px 2px 2px #b8b8b8", "textShadow": "1px 1px 2px #282828"}, "quickReply": {"background": "#e6f0f7", "selectedBackground": "#352296", "color": "#ffffff", "fontWeight": "600", "boxShadow": "2px 2px 2px #b8b8b8", "textShadow": "1px 1px 2px #282828", "border": "0px solid red"}, "nextButton": {"background": "#da1c5c", "color": "#ffffff", "fontWeight": "", "boxShadow": "2px 2px 2px #b8b8b8", "textShadow": "", "border": "0px solid red", "text": "Next", "padding": "0px 20px"}, "backButton": {"color": "#da1c5c", "fontWeight": "", "textShadow": "", "text": "Back", "svgIcon": "<svg width='16' height='16' viewBox='0 0 16 16' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M13.3334 7.33341H5.22008L8.94675 3.60675L8.00008 2.66675L2.66675 8.00008L8.00008 13.3334L8.94008 12.3934L5.22008 8.66675H13.3334V7.33341Z' fill='#da1c5c'/></svg>"}, "interactionIcon": {"background": "#da1c5c", "boxShadow": "2px 2px 2px #b8b8b8", "svgImageContent": "<svg width='14' height='14' viewBox='0 0 14 14' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M2.66659 2.66667H11.3333V9.16667H3.30034L2.66659 9.80042V2.66667ZM2.66659 1.58334C2.07075 1.58334 1.58867 2.07084 1.58867 2.66667L1.58325 12.4167L3.74992 10.25H11.3333C11.9291 10.25 12.4166 9.7625 12.4166 9.16667V2.66667C12.4166 2.07084 11.9291 1.58334 11.3333 1.58334H2.66659ZM3.74992 7H8.08325V8.08334H3.74992V7ZM3.74992 5.375H10.2499V6.45834H3.74992V5.375ZM3.74992 3.75H10.2499V4.83334H3.74992V3.75Z' fill='white' /></svg>"}, "contentContainer": {"border": "", "boxShadow": "2px 2px 4px #b8b8b8", "height": "700px", "inheritParentHeight": false}, "container": {"border": "", "boxShadow": "", "borderRadius": "30px"}, "refresh": {"enabled": true, "confirmationPromptText": "Do you want to start over?", "confirmationCancelText": "Nay", "confirmationText": "Yey", "shouldClearHistory": true}, "close": {"enabled": true, "confirmationPromptText": "Do you want to close smart form?", "shouldRemoveComponent": true, "confirmationCancelText": "Nope", "confirmationText": "Yep"}, "typography": {"fontFamily": "<PERSON>l, sans-serif"}, "fileUpload": {"defaultText": "Drop your document here, or select", "limitDescriptionLabel": "Select up to __MAX_FILE_COUNT__ files (each file size up to __MAX_FILE_SIZE__ MB)", "fileSizeErrorText": "Selected file(s) exceeds the size limit", "fileCountErrorText": "Maximum number of files selected", "corruptFileTooltip": "File is corrupted. Please try again", "existingFileErrorText": "File is already selected", "fileUploadingText": "Files are being uploaded. Please wait", "fileUploadSuccessText": "Your license was uploaded successfully.", "fileUploadFailText": "Error while uploading the file. Please try again", "background": "#352296", "hoverBackground": "#004070", "color": "#ffffff", "disabledBackground": "#BFCCD9", "fontFamily": "Lexend", "fontSize": "14px", "fontWeight": "400", "lineHeight": "24px", "letterSpacing": "-0.14px", "boxShadow": "none", "textShadow": "none", "borderRadius": "4px", "border": "none", "padding": "5px 10px", "text": "Upload", "cursor": "pointer", "btnText": "Browse", "selectedFileText": "ENSelected files"}}, "GEN_AGENTS_DATA": {"FILE_UPLOAD": {"DEFAULT_TEXT": "Drag & Drop your Files here, or select", "LIMIT_DESCRIPTION_LABEL": "Select up to __MAX_FILE_COUNT__ files (each file size up to __MAX_FILE_SIZE__ MB)", "FILE_SIZE_ERROR_TEXT": "Selected file(s) exceeds the size limit", "FILE_COUNT_ERROR_TEXT": "Maximum number of files selected", "EXISTING_FILE_ERROR_TEXT": "File is already selected", "FILE_UPLOADING_TEXT": "Files are being uploaded. Please wait...", "FILE_UPLOAD_SUCCESS_TEXT": "Your license was uploaded successfully.", "FILE_UPLOAD_FAIL_TEXT": "Error while uploading the file. Please try again", "BACKGROUND": "#352296", "HOVER_BACKGROUND": "#004070", "COLOR": "#ffffff", "DISABLED_BACKGROUND": "#BFCCD9", "FONT_FAMILY": "Lexend", "FONT_SIZE": "14px", "FONT_WEIGHT": "400", "LINE_HEIGHT": "24px", "LETTER_SPACING": "-0.14px", "BOX_SHADOW": "none", "TEXT_SHADOW": "none", "BORDER_RADIUS": "100px", "BORDER": "none", "PADDING": "5px 12px", "TEXT": "Upload", "CURSOR": "pointer", "BTN_TEXT": "Browse", "UPLOADED_FILE_TEXT": "Uploaded License"}}}